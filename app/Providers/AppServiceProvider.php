<?php

declare(strict_types=1);

namespace App\Providers;

use App\Models\User;
use Aws\Kms\KmsClient;
use Aws\Sqs\SqsClient;
use Elastic\Elasticsearch\Client;
use Elastic\Elasticsearch\ClientBuilder;
use Guz<PERSON>Http\Client as GuzzleClient;
use Psr\Http\Client\ClientInterface;
use Illuminate\Auth\Middleware\RedirectIfAuthenticated;
use Illuminate\Auth\Notifications\ResetPassword;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\ServiceProvider;
use Lab404\Impersonate\Events\LeaveImpersonation;
use Lab404\Impersonate\Events\TakeImpersonation;

final class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Bind PSR-18 HTTP Client for Elasticsearch as singleton
        $this->app->singleton(ClientInterface::class, fn () => new GuzzleClient());
        
        // Bind Elasticsearch Client as singleton
        $this->app->singleton(Client::class, function () {
            $config = config('scout.elasticsearch', []);
            
            // Set defaults if config is empty
            $hosts = $config['hosts'] ?? ['localhost:9200'];
            $retries = $config['retries'] ?? 2;
            
            $builder = ClientBuilder::create()
                ->setHosts($hosts)
                ->setRetries($retries);

            if (isset($config['basicAuthentication']) && is_array($config['basicAuthentication'])) {
                $builder->setBasicAuthentication($config['basicAuthentication'][0], $config['basicAuthentication'][1]);
            }

            if (isset($config['logger']) && !is_object($config['logger'])) {
                $builder->setLogger($config['logger']);
            }

            return $builder->build();
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        if ($this->app->environment('local')) {
            $this->app->register(\Laravel\Telescope\TelescopeServiceProvider::class);
            $this->app->register(TelescopeServiceProvider::class);
        }

        RedirectIfAuthenticated::redirectUsing(fn () => '/api/users/me');

        ResetPassword::createUrlUsing(function (object $notifiable, string $token) {
            return config('app.frontend_url')."/password-reset/$token?email={$notifiable->getEmailForPasswordReset()}";
        });

        JsonResource::withoutWrapping();

        Gate::define('viewApiDocs', function (User $user) {
            return in_array($user->email, ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']);
        });

        Gate::define('viewPulse', function (User $user) {
            return in_array($user->email, ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>']);
        });

        Schema::morphUsingUuids();

        // Bindings
        $binds = [
            KmsClient::class => fn () => new KmsClient([
                'version' => '2014-11-01',
            ]),
            SqsClient::class => fn () => new SqsClient([
                'version' => '2012-11-05',
            ]),
        ];

        foreach ($binds as $abstract => $concrete) {
            $this->app->bind($abstract, $concrete);
        }

        // https://github.com/404labfr/laravel-impersonate/issues/219#issuecomment-2689990251
        Event::listen(function (TakeImpersonation $event) {
            session()->put([
                'password_hash_web' => $event->impersonated->getAuthPassword(),
            ]);
        });

        Event::listen(function (LeaveImpersonation $event) {
            session()->remove('password_hash_web');
            session()->put([
                'password_hash_web' => $event->impersonator->getAuthPassword(),
            ]);
            Auth::setUser($event->impersonator);
        });
    }
}
